#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文字颜色分析工具
专门分析交易信号图像中的文字颜色特征，区分文字和线框
"""

import cv2
import numpy as np
import os
from typing import Dict, List, Tuple
import json
from datetime import datetime


class TextColorAnalyzer:
    """文字颜色分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.signal_mapping = {
            '1.png': '持仓', '5.png': '持仓',
            '2.png': '清仓', '6.png': '清仓', 
            '3.png': '开仓', '7.png': '开仓',
            '4.png': '空仓', '8.png': '空仓'
        }
        
        # 预期的文字颜色（根据您的描述）
        self.expected_colors = {
            '开仓': '红色',
            '清仓': '绿色',
            '持仓': '橙色',
            '空仓': '灰白色'
        }
    
    def detect_text_regions(self, image: np.ndarray) -> List[Tuple]:
        """检测文字区域，排除线框"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用形态学操作检测文字区域
        # 文字通常是连续的像素块
        kernel_text = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        
        # 二值化
        _, binary = cv2.threshold(gray, 30, 255, cv2.THRESH_BINARY)
        
        # 形态学闭运算连接文字像素
        text_mask = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel_text)
        
        # 查找轮廓
        contours, _ = cv2.findContours(text_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        text_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            # 过滤太小的区域（噪声）和太大的区域（可能是线框）
            if 10 < area < 500:
                x, y, w, h = cv2.boundingRect(contour)
                # 文字区域通常宽高比合理
                aspect_ratio = w / h if h > 0 else 0
                if 0.2 < aspect_ratio < 5.0:
                    text_regions.append((x, y, w, h, area))
        
        return text_regions
    
    def detect_line_regions(self, image: np.ndarray) -> List[Tuple]:
        """检测线框区域"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用Canny边缘检测
        edges = cv2.Canny(gray, 50, 150)
        
        # 使用霍夫线变换检测直线
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=20, minLineLength=10, maxLineGap=5)
        
        line_regions = []
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                # 计算线段的边界框
                x_min, x_max = min(x1, x2), max(x1, x2)
                y_min, y_max = min(y1, y2), max(y1, y2)
                w = x_max - x_min + 2  # 加一点边距
                h = y_max - y_min + 2
                line_regions.append((x_min-1, y_min-1, w, h))
        
        return line_regions
    
    def extract_text_colors(self, image: np.ndarray, text_regions: List[Tuple]) -> List[Dict]:
        """提取文字区域的颜色"""
        hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        text_colors = []
        
        for i, (x, y, w, h, area) in enumerate(text_regions):
            # 提取文字区域
            text_roi = hsv_image[y:y+h, x:x+w]
            
            if text_roi.size == 0:
                continue
            
            # 排除黑色背景
            non_black_mask = cv2.inRange(text_roi, np.array([0, 0, 30]), np.array([180, 255, 255]))
            
            if cv2.countNonZero(non_black_mask) == 0:
                continue
            
            # 获取非黑色像素
            non_black_pixels = text_roi[non_black_mask > 0]
            
            if len(non_black_pixels) == 0:
                continue
            
            # 计算主要颜色
            unique_colors, counts = np.unique(non_black_pixels, axis=0, return_counts=True)
            dominant_idx = np.argmax(counts)
            dominant_color = unique_colors[dominant_idx]
            
            # 计算颜色统计
            h_values = non_black_pixels[:, 0]
            s_values = non_black_pixels[:, 1]
            v_values = non_black_pixels[:, 2]
            
            color_info = {
                'region_id': i,
                'bbox': [int(x), int(y), int(w), int(h)],
                'area': int(area),
                'dominant_color_hsv': [int(dominant_color[0]), int(dominant_color[1]), int(dominant_color[2])],
                'pixel_count': int(len(non_black_pixels)),
                'h_stats': {
                    'mean': float(np.mean(h_values)),
                    'std': float(np.std(h_values)),
                    'min': int(h_values.min()),
                    'max': int(h_values.max())
                },
                's_stats': {
                    'mean': float(np.mean(s_values)),
                    'std': float(np.std(s_values)),
                    'min': int(s_values.min()),
                    'max': int(s_values.max())
                },
                'v_stats': {
                    'mean': float(np.mean(v_values)),
                    'std': float(np.std(v_values)),
                    'min': int(v_values.min()),
                    'max': int(v_values.max())
                }
            }
            
            text_colors.append(color_info)
        
        return text_colors
    
    def classify_color_by_hsv(self, hsv_color: List[int]) -> str:
        """根据HSV值分类颜色"""
        h, s, v = hsv_color
        
        # 基于HSV值判断颜色类型
        if s < 50:  # 低饱和度
            if v > 150:
                return '灰白色'
            else:
                return '灰色'
        elif s >= 50:  # 高饱和度
            if h <= 10 or h >= 170:  # 红色范围
                return '红色'
            elif 35 <= h <= 85:  # 绿色范围
                return '绿色'
            elif 10 < h < 35:  # 橙色/黄色范围
                if v > 150:
                    return '橙色'
                else:
                    return '黄色'
            elif 85 < h < 130:  # 青色/蓝色范围
                return '蓝色'
            else:
                return '其他颜色'
        
        return '未知颜色'
    
    def analyze_image(self, image_path: str) -> Dict:
        """分析单张图像"""
        print(f"\n分析图像: {image_path}")
        
        image = cv2.imread(image_path)
        if image is None:
            return {'error': f'无法读取图像: {image_path}'}
        
        print(f"图像尺寸: {image.shape}")
        
        # 检测文字区域
        text_regions = self.detect_text_regions(image)
        print(f"检测到 {len(text_regions)} 个文字区域")
        
        # 检测线框区域
        line_regions = self.detect_line_regions(image)
        print(f"检测到 {len(line_regions)} 个线框区域")
        
        # 提取文字颜色
        text_colors = self.extract_text_colors(image, text_regions)
        
        # 分析颜色
        color_analysis = []
        for color_info in text_colors:
            hsv_color = color_info['dominant_color_hsv']
            color_name = self.classify_color_by_hsv(hsv_color)
            
            analysis = {
                'region_info': color_info,
                'color_classification': color_name,
                'hsv_color': hsv_color
            }
            color_analysis.append(analysis)
            
            print(f"  区域 {color_info['region_id']}: HSV{hsv_color} -> {color_name}")
        
        # 确定主要文字颜色
        if color_analysis:
            # 选择面积最大的文字区域作为主要颜色
            main_color_info = max(color_analysis, key=lambda x: x['region_info']['area'])
            main_color = main_color_info['color_classification']
        else:
            main_color = '无法检测'
        
        filename = os.path.basename(image_path)
        expected_signal = self.signal_mapping.get(filename, '未知')
        expected_color = self.expected_colors.get(expected_signal, '未知')
        
        print(f"  主要文字颜色: {main_color}")
        print(f"  预期信号: {expected_signal}")
        print(f"  预期颜色: {expected_color}")
        print(f"  颜色匹配: {'✓' if self.color_matches_signal(main_color, expected_signal) else '✗'}")
        
        return {
            'filename': filename,
            'image_size': [int(image.shape[0]), int(image.shape[1]), int(image.shape[2])],
            'text_regions_count': len(text_regions),
            'line_regions_count': len(line_regions),
            'text_regions': [[int(x), int(y), int(w), int(h), int(area)] for x, y, w, h, area in text_regions],
            'line_regions': [[int(x), int(y), int(w), int(h)] for x, y, w, h in line_regions],
            'color_analysis': color_analysis,
            'main_text_color': main_color,
            'expected_signal': expected_signal,
            'expected_color': expected_color,
            'color_match': self.color_matches_signal(main_color, expected_signal)
        }
    
    def color_matches_signal(self, detected_color: str, expected_signal: str) -> bool:
        """检查检测到的颜色是否与预期信号匹配"""
        color_signal_map = {
            '红色': '开仓',
            '绿色': '清仓',
            '橙色': '持仓',
            '灰白色': '空仓',
            '灰色': '空仓'  # 灰色也可能是空仓
        }
        
        predicted_signal = color_signal_map.get(detected_color, '未知')
        return predicted_signal == expected_signal
    
    def analyze_all_images(self, image_dir: str) -> Dict:
        """分析所有图像"""
        print("=" * 80)
        print("文字颜色特征分析")
        print("=" * 80)
        
        results = []
        correct_count = 0
        total_count = 0
        
        for filename in sorted(os.listdir(image_dir)):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_path = os.path.join(image_dir, filename)
                result = self.analyze_image(image_path)
                results.append(result)
                
                if 'error' not in result:
                    total_count += 1
                    if result['color_match']:
                        correct_count += 1
        
        accuracy = correct_count / total_count if total_count > 0 else 0
        
        print(f"\n" + "=" * 80)
        print("分析总结")
        print("=" * 80)
        print(f"总图像数: {total_count}")
        print(f"正确识别: {correct_count}")
        print(f"准确率: {accuracy:.2%}")
        
        # 生成颜色范围建议
        color_ranges = self.generate_color_ranges(results)
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_images': total_count,
            'correct_predictions': correct_count,
            'accuracy': accuracy,
            'results': results,
            'suggested_color_ranges': color_ranges
        }
        
        return summary
    
    def generate_color_ranges(self, results: List[Dict]) -> Dict:
        """基于分析结果生成颜色范围"""
        signal_colors = {
            '持仓': [],
            '清仓': [],
            '开仓': [],
            '空仓': []
        }
        
        # 收集每种信号的颜色数据
        for result in results:
            if 'error' in result:
                continue
            
            expected_signal = result['expected_signal']
            if expected_signal in signal_colors and result['color_analysis']:
                # 取面积最大的文字区域
                main_color_info = max(result['color_analysis'], 
                                    key=lambda x: x['region_info']['area'])
                hsv_color = main_color_info['hsv_color']
                signal_colors[expected_signal].append(hsv_color)
        
        # 为每种信号生成颜色范围
        color_ranges = {}
        for signal, colors in signal_colors.items():
            if colors:
                colors_array = np.array(colors)
                
                # 计算每个通道的范围
                h_values = colors_array[:, 0]
                s_values = colors_array[:, 1]
                v_values = colors_array[:, 2]
                
                # 使用均值和标准差来设定范围
                h_mean, h_std = np.mean(h_values), np.std(h_values)
                s_mean, s_std = np.mean(s_values), np.std(s_values)
                v_mean, v_std = np.mean(v_values), np.std(v_values)
                
                # 设定范围（均值 ± 2倍标准差，但限制在有效范围内）
                h_range = max(5, min(20, 2 * h_std))
                s_range = max(30, min(80, 2 * s_std))
                v_range = max(30, min(80, 2 * v_std))
                
                lower = [
                    max(0, int(h_mean - h_range)),
                    max(0, int(s_mean - s_range)),
                    max(0, int(v_mean - v_range))
                ]
                upper = [
                    min(179, int(h_mean + h_range)),
                    min(255, int(s_mean + s_range)),
                    min(255, int(v_mean + v_range))
                ]
                
                color_ranges[signal] = {
                    'lower': lower,
                    'upper': upper,
                    'mean_hsv': [int(h_mean), int(s_mean), int(v_mean)],
                    'sample_count': len(colors)
                }
        
        return color_ranges


def main():
    """主函数"""
    analyzer = TextColorAnalyzer()
    
    image_dir = "signle"
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录 '{image_dir}' 不存在")
        return
    
    # 分析所有图像
    summary = analyzer.analyze_all_images(image_dir)
    
    # 保存分析结果
    output_file = "text_color_analysis.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析结果已保存到: {output_file}")
    
    # 保存建议的颜色配置
    if summary['suggested_color_ranges']:
        config_file = "text_based_color_config.json"
        config = {
            'signal_types': {
                'hold': '持仓',
                'sell': '清仓',
                'buy': '开仓', 
                'empty': '空仓'
            },
            'color_ranges_hsv': {}
        }
        
        signal_mapping = {'持仓': 'hold', '清仓': 'sell', '开仓': 'buy', '空仓': 'empty'}
        
        for signal_cn, signal_en in signal_mapping.items():
            if signal_cn in summary['suggested_color_ranges']:
                range_data = summary['suggested_color_ranges'][signal_cn]
                config['color_ranges_hsv'][signal_en] = {
                    'description': f'{signal_cn}信号 - 基于文字颜色分析',
                    'lower': range_data['lower'],
                    'upper': range_data['upper'],
                    'mean_hsv': range_data['mean_hsv']
                }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"建议的颜色配置已保存到: {config_file}")


if __name__ == "__main__":
    main()
