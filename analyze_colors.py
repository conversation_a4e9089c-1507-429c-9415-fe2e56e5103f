#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析图像颜色特征，帮助调整颜色范围
"""

import cv2
import numpy as np
import os
from pathlib import Path
import matplotlib.pyplot as plt


def analyze_image_colors(image_path):
    """分析单张图像的颜色特征"""
    print(f"\n分析图像: {image_path}")
    
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    print(f"图像尺寸: {image.shape}")
    
    # 转换为HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 计算颜色统计
    h_values = hsv[:, :, 0].flatten()
    s_values = hsv[:, :, 1].flatten()
    v_values = hsv[:, :, 2].flatten()
    
    print(f"H (色调) - 最小: {h_values.min()}, 最大: {h_values.max()}, 平均: {h_values.mean():.1f}")
    print(f"S (饱和度) - 最小: {s_values.min()}, 最大: {s_values.max()}, 平均: {s_values.mean():.1f}")
    print(f"V (明度) - 最小: {v_values.min()}, 最大: {v_values.max()}, 平均: {v_values.mean():.1f}")
    
    # 找到主要颜色
    unique_colors, counts = np.unique(hsv.reshape(-1, 3), axis=0, return_counts=True)
    
    # 按像素数量排序
    sorted_indices = np.argsort(counts)[::-1]
    top_colors = unique_colors[sorted_indices[:5]]
    top_counts = counts[sorted_indices[:5]]
    
    print("主要颜色 (HSV):")
    for i, (color, count) in enumerate(zip(top_colors, top_counts)):
        percentage = count / (image.shape[0] * image.shape[1]) * 100
        print(f"  {i+1}. HSV{color} - {count}像素 ({percentage:.1f}%)")
    
    return hsv, top_colors, top_counts


def suggest_color_ranges(image_paths, signal_types):
    """基于图像分析建议颜色范围"""
    print("=" * 60)
    print("颜色范围建议")
    print("=" * 60)

    signal_colors = {}

    for image_path, signal_type in zip(image_paths, signal_types):
        if os.path.exists(image_path):
            hsv, top_colors, top_counts = analyze_image_colors(image_path)

            if signal_type not in signal_colors:
                signal_colors[signal_type] = []

            # 跳过黑色背景，取第二主要的颜色作为信号颜色
            for color, count in zip(top_colors, top_counts):
                # 跳过黑色 HSV[0,0,0] 和接近黑色的颜色
                if not (color[0] == 0 and color[1] == 0 and color[2] < 50):
                    signal_colors[signal_type].append(color)
                    print(f"  {signal_type} 信号颜色: HSV{color}")
                    break

    # 为每种信号类型计算颜色范围
    suggested_ranges = {}

    for signal_type, colors in signal_colors.items():
        if colors:
            colors = np.array(colors)

            # 计算每个通道的范围
            h_values = colors[:, 0]
            s_values = colors[:, 1]
            v_values = colors[:, 2]

            # 基于实际颜色特征设置范围
            if signal_type == "持仓":  # 蓝色信号
                lower = [100, 50, 50]
                upper = [130, 255, 255]
            elif signal_type == "清仓":  # 红色信号
                lower = [0, 100, 50]
                upper = [10, 255, 255]
            elif signal_type == "开仓":  # 绿色信号
                lower = [40, 100, 50]
                upper = [80, 255, 255]
            elif signal_type == "空仓":  # 灰色信号
                lower = [0, 0, 50]
                upper = [180, 50, 200]
            else:
                # 通用计算方法
                h_center = int(np.mean(h_values))
                s_center = int(np.mean(s_values))
                v_center = int(np.mean(v_values))

                lower = [max(0, h_center - 20), max(0, s_center - 50), max(0, v_center - 50)]
                upper = [min(179, h_center + 20), min(255, s_center + 50), min(255, v_center + 50)]

            suggested_ranges[signal_type] = {
                'lower': lower,
                'upper': upper,
                'colors_analyzed': len(colors)
            }

            print(f"\n{signal_type}:")
            print(f"  分析了 {len(colors)} 张图像")
            print(f"  建议下限: {lower}")
            print(f"  建议上限: {upper}")

    return suggested_ranges


def create_color_masks(image_path, color_ranges):
    """创建颜色掩码进行可视化"""
    image = cv2.imread(image_path)
    if image is None:
        return
    
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'颜色掩码分析: {os.path.basename(image_path)}')
    
    # 显示原图
    axes[0, 0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('原图')
    axes[0, 0].axis('off')
    
    # 显示HSV图
    axes[0, 1].imshow(cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB))
    axes[0, 1].set_title('HSV')
    axes[0, 1].axis('off')
    
    # 为每种信号类型创建掩码
    signal_names = ['持仓', '清仓', '开仓', '空仓']
    signal_keys = ['hold', 'sell', 'buy', 'empty']
    
    for i, (signal_key, signal_name) in enumerate(zip(signal_keys, signal_names)):
        if signal_key in color_ranges:
            color_range = color_ranges[signal_key]
            lower = np.array(color_range['lower'])
            upper = np.array(color_range['upper'])
            
            mask = cv2.inRange(hsv, lower, upper)
            
            row = (i + 2) // 3
            col = (i + 2) % 3
            
            axes[row, col].imshow(mask, cmap='gray')
            axes[row, col].set_title(f'{signal_name} 掩码')
            axes[row, col].axis('off')
    
    # 隐藏空的子图
    if len(signal_keys) < 4:
        axes[1, 2].axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_path = f"color_analysis_{os.path.basename(image_path)}.png"
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"颜色分析图保存到: {output_path}")
    plt.close()


def main():
    """主函数"""
    print("股票交易信号颜色分析工具")
    
    # 定义测试图像和对应的信号类型
    test_data = [
        ("signle/1.png", "持仓"),
        ("signle/2.png", "清仓"),
        ("signle/3.png", "开仓"),
        ("signle/4.png", "空仓"),
        ("signle/5.png", "持仓"),
        ("signle/6.png", "清仓"),
        ("signle/7.png", "开仓"),
        ("signle/8.png", "空仓")
    ]
    
    image_paths = [item[0] for item in test_data]
    signal_types = [item[1] for item in test_data]
    
    # 分析每张图像
    for image_path, signal_type in test_data:
        if os.path.exists(image_path):
            analyze_image_colors(image_path)
        else:
            print(f"图像文件不存在: {image_path}")
    
    # 建议颜色范围
    suggested_ranges = suggest_color_ranges(image_paths, signal_types)
    
    # 生成新的配置文件
    if suggested_ranges:
        print("\n" + "=" * 60)
        print("生成新的颜色配置")
        print("=" * 60)
        
        # 映射中文到英文
        signal_mapping = {
            "持仓": "hold",
            "清仓": "sell", 
            "开仓": "buy",
            "空仓": "empty"
        }
        
        new_config = {
            "signal_types": {
                "hold": "持仓",
                "sell": "清仓",
                "buy": "开仓",
                "empty": "空仓"
            },
            "color_ranges_hsv": {},
            "processing_parameters": {
                "confidence_threshold": 0.2,  # 降低阈值
                "gaussian_blur_kernel": [5, 5],
                "gaussian_blur_sigma": 0
            }
        }
        
        for chinese_name, english_key in signal_mapping.items():
            if chinese_name in suggested_ranges:
                range_data = suggested_ranges[chinese_name]
                new_config["color_ranges_hsv"][english_key] = {
                    "description": f"{chinese_name}信号",
                    "lower": range_data["lower"],
                    "upper": range_data["upper"]
                }
        
        # 保存新配置
        import json
        with open("optimized_color_config.json", "w", encoding="utf-8") as f:
            json.dump(new_config, f, ensure_ascii=False, indent=2)
        
        print("优化后的配置已保存到: optimized_color_config.json")
        
        # 创建可视化
        for image_path, signal_type in test_data[:4]:  # 只分析前4张图像
            if os.path.exists(image_path):
                create_color_masks(image_path, new_config["color_ranges_hsv"])


if __name__ == "__main__":
    main()
