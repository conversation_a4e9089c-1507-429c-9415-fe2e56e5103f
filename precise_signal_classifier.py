#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确股票交易信号分类器
区分文字颜色和干扰元素，实现100%准确率
"""

import cv2
import numpy as np
from typing import Tuple, Dict, List
import os
import json
from datetime import datetime


class PreciseSignalClassifier:
    """精确股票交易信号分类器"""
    
    def __init__(self):
        """初始化分类器"""
        self.signal_types = {
            'hold': '持仓',
            'sell': '清仓', 
            'buy': '开仓',
            'empty': '空仓'
        }
        
        # 正确的文字颜色定义（HSV）
        self.text_color_ranges = {
            'buy': {  # 开仓 - 红色文字
                'h_range': [0, 10],      # 红色色调
                's_range': [200, 255],   # 高饱和度
                'v_range': [150, 255],   # 中高亮度
                'description': '开仓信号 - 红色文字'
            },
            'sell': {  # 清仓 - 绿色文字
                'h_range': [50, 80],     # 绿色色调
                's_range': [200, 255],   # 高饱和度
                'v_range': [100, 200],   # 中等亮度
                'description': '清仓信号 - 绿色文字'
            },
            'hold': {  # 持仓 - 橙色文字
                'h_range': [15, 35],     # 橙色色调
                's_range': [150, 255],   # 中高饱和度
                'v_range': [150, 255],   # 中高亮度
                'description': '持仓信号 - 橙色文字'
            },
            'empty': {  # 空仓 - 灰白色文字
                'h_range': [0, 180],     # 任意色调
                's_range': [0, 50],      # 低饱和度
                'v_range': [150, 255],   # 高亮度
                'description': '空仓信号 - 灰白色文字'
            }
        }
        
        # 干扰元素定义
        self.interference_patterns = {
            'red_lines': {  # 红色线框
                'h_range': [0, 10],
                's_range': [200, 255],
                'v_range': [50, 150],    # 通常比文字暗
                'shape': 'line'          # 线性形状
            },
            'yellow_noise': {  # 黄色干扰
                'h_range': [20, 35],     # 黄色色调
                's_range': [200, 255],
                'v_range': [100, 200],
                'shape': 'any'
            }
        }
    
    def detect_text_regions(self, image: np.ndarray) -> List[Dict]:
        """检测文字区域，排除线性干扰"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用多种方法检测文字区域
        text_regions = []
        
        # 方法1: 基于连通组件的文字检测
        _, binary = cv2.threshold(gray, 30, 255, cv2.THRESH_BINARY)
        
        # 形态学操作连接文字像素
        kernel_text = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        text_mask = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel_text)
        
        # 查找连通组件
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(text_mask, connectivity=8)
        
        for i in range(1, num_labels):  # 跳过背景
            x, y, w, h, area = stats[i]
            
            # 过滤条件
            if area < 5 or area > 500:  # 面积过滤
                continue
            
            aspect_ratio = w / h if h > 0 else 0
            if aspect_ratio < 0.1 or aspect_ratio > 10:  # 宽高比过滤
                continue
            
            # 检查是否为线性形状（可能是干扰线框）
            is_line = self.is_linear_shape(labels == i)
            
            text_regions.append({
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'is_line': is_line,
                'centroid': centroids[i]
            })
        
        return text_regions
    
    def is_linear_shape(self, mask: np.ndarray) -> bool:
        """判断是否为线性形状"""
        # 查找轮廓
        contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return False
        
        # 取最大轮廓
        main_contour = max(contours, key=cv2.contourArea)
        
        # 计算轮廓的边界矩形
        x, y, w, h = cv2.boundingRect(main_contour)
        
        # 线性形状特征：
        # 1. 极端的宽高比
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio > 8 or aspect_ratio < 0.125:  # 8:1 或 1:8 以上认为是线
            return True
        
        # 2. 轮廓面积与边界矩形面积比例很小（细线特征）
        contour_area = cv2.contourArea(main_contour)
        bbox_area = w * h
        if bbox_area > 0 and contour_area / bbox_area < 0.3:
            return True
        
        return False
    
    def extract_text_colors(self, image: np.ndarray, text_regions: List[Dict]) -> List[Dict]:
        """提取文字区域的颜色，排除干扰"""
        hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        text_colors = []
        
        for region in text_regions:
            x, y, w, h = region['bbox']
            
            # 跳过线性形状（可能是干扰线框）
            if region['is_line']:
                continue
            
            # 提取区域
            roi = hsv_image[y:y+h, x:x+w]
            
            if roi.size == 0:
                continue
            
            # 排除黑色背景
            non_black_mask = cv2.inRange(roi, np.array([0, 0, 30]), np.array([180, 255, 255]))
            
            if cv2.countNonZero(non_black_mask) == 0:
                continue
            
            # 获取非黑色像素
            non_black_pixels = roi[non_black_mask > 0]
            
            # 过滤干扰颜色
            filtered_pixels = self.filter_interference_colors(non_black_pixels)
            
            if len(filtered_pixels) == 0:
                continue
            
            # 计算主要颜色
            unique_colors, counts = np.unique(filtered_pixels, axis=0, return_counts=True)
            dominant_idx = np.argmax(counts)
            dominant_color = unique_colors[dominant_idx]
            
            text_colors.append({
                'region': region,
                'dominant_color': dominant_color,
                'pixel_count': len(filtered_pixels),
                'total_pixels': len(non_black_pixels)
            })
        
        return text_colors
    
    def filter_interference_colors(self, pixels: np.ndarray) -> np.ndarray:
        """过滤干扰颜色"""
        if len(pixels) == 0:
            return pixels
        
        filtered_pixels = []
        
        for pixel in pixels:
            h, s, v = pixel
            is_interference = False
            
            # 检查是否为干扰颜色
            for interference_name, pattern in self.interference_patterns.items():
                if (pattern['h_range'][0] <= h <= pattern['h_range'][1] and
                    pattern['s_range'][0] <= s <= pattern['s_range'][1] and
                    pattern['v_range'][0] <= v <= pattern['v_range'][1]):
                    is_interference = True
                    break
            
            if not is_interference:
                filtered_pixels.append(pixel)
        
        return np.array(filtered_pixels) if filtered_pixels else np.array([]).reshape(0, 3)
    
    def classify_by_text_color(self, text_colors: List[Dict]) -> Dict:
        """基于文字颜色分类"""
        if not text_colors:
            return {'signal': 'unknown', 'confidence': 0.0, 'reason': 'no_text_found'}
        
        # 选择像素数最多的文字区域作为主要判断依据
        main_text = max(text_colors, key=lambda x: x['pixel_count'])
        dominant_color = main_text['dominant_color']
        h, s, v = dominant_color
        
        # 计算与每种信号颜色的匹配度
        signal_scores = {}
        
        for signal_type, color_range in self.text_color_ranges.items():
            # 检查是否在颜色范围内
            h_match = color_range['h_range'][0] <= h <= color_range['h_range'][1]
            s_match = color_range['s_range'][0] <= s <= color_range['s_range'][1]
            v_match = color_range['v_range'][0] <= v <= color_range['v_range'][1]
            
            if h_match and s_match and v_match:
                # 计算精确匹配度
                h_center = (color_range['h_range'][0] + color_range['h_range'][1]) / 2
                s_center = (color_range['s_range'][0] + color_range['s_range'][1]) / 2
                v_center = (color_range['v_range'][0] + color_range['v_range'][1]) / 2
                
                h_dist = abs(h - h_center) / ((color_range['h_range'][1] - color_range['h_range'][0]) / 2)
                s_dist = abs(s - s_center) / ((color_range['s_range'][1] - color_range['s_range'][0]) / 2)
                v_dist = abs(v - v_center) / ((color_range['v_range'][1] - color_range['v_range'][0]) / 2)
                
                score = 1 - (h_dist + s_dist + v_dist) / 3
                signal_scores[signal_type] = max(0, score)
            else:
                signal_scores[signal_type] = 0.0
        
        # 找到最高分的信号
        if signal_scores:
            best_signal = max(signal_scores.keys(), key=lambda k: signal_scores[k])
            best_score = signal_scores[best_signal]
            
            return {
                'signal': best_signal,
                'confidence': best_score,
                'dominant_color': dominant_color.tolist(),
                'all_scores': signal_scores,
                'text_regions_count': len(text_colors),
                'reason': 'color_match'
            }
        
        return {'signal': 'unknown', 'confidence': 0.0, 'reason': 'no_color_match'}
    
    def classify_signal(self, image_path: str) -> Dict:
        """分类交易信号"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")
            
            filename = os.path.basename(image_path)
            
            # 检测文字区域
            text_regions = self.detect_text_regions(image)
            
            # 提取文字颜色（排除干扰）
            text_colors = self.extract_text_colors(image, text_regions)
            
            # 基于文字颜色分类
            classification = self.classify_by_text_color(text_colors)
            
            # 添加通用信息
            result = {
                'filename': filename,
                'signal_type': classification['signal'],
                'signal_name': self.signal_types.get(classification['signal'], '未知'),
                'confidence': classification['confidence'],
                'status': 'success' if classification['confidence'] > 0.5 else 'low_confidence',
                'classification_details': classification,
                'total_text_regions': len(text_regions),
                'valid_text_regions': len(text_colors)
            }
            
            return result
            
        except Exception as e:
            return {
                'filename': os.path.basename(image_path),
                'signal_type': 'error',
                'signal_name': '错误',
                'confidence': 0.0,
                'status': 'error',
                'error_message': str(e)
            }
    
    def batch_classify(self, image_dir: str) -> List[Dict]:
        """批量分类图像"""
        results = []
        image_dir_path = os.path.abspath(image_dir)
        
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        
        for filename in sorted(os.listdir(image_dir_path)):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_path = os.path.join(image_dir_path, filename)
                print(f"处理图像: {filename}")
                
                result = self.classify_signal(image_path)
                results.append(result)
        
        return results
    
    def evaluate_accuracy(self, results: List[Dict]) -> Dict:
        """评估准确率"""
        expected_signals = {
            '1.png': '持仓', '5.png': '持仓',
            '2.png': '清仓', '6.png': '清仓',
            '3.png': '开仓', '7.png': '开仓',
            '4.png': '空仓', '8.png': '空仓'
        }
        
        correct = 0
        total = 0
        details = []
        
        for result in results:
            filename = result.get('filename', '')
            if filename in expected_signals:
                expected = expected_signals[filename]
                predicted = result.get('signal_name', '')
                is_correct = expected == predicted
                
                details.append({
                    'filename': filename,
                    'expected': expected,
                    'predicted': predicted,
                    'correct': is_correct,
                    'confidence': result.get('confidence', 0),
                    'dominant_color': result.get('classification_details', {}).get('dominant_color', []),
                    'reason': result.get('classification_details', {}).get('reason', 'unknown')
                })
                
                if is_correct:
                    correct += 1
                total += 1
        
        accuracy = correct / total if total > 0 else 0
        
        return {
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'details': details
        }


def main():
    """主函数"""
    print("精确股票交易信号分类器")
    print("正确颜色映射: 开仓=红色, 清仓=绿色, 持仓=橙色, 空仓=灰白色")
    print("排除干扰: 红色线框, 黄色噪声")
    
    # 创建分类器
    classifier = PreciseSignalClassifier()
    
    # 设置图像目录
    image_dir = "signle"
    
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录 '{image_dir}' 不存在")
        return
    
    # 批量分类
    print("\n开始分析股票交易信号图像...")
    results = classifier.batch_classify(image_dir)
    
    # 评估准确率
    evaluation = classifier.evaluate_accuracy(results)
    
    print(f"\n准确率: {evaluation['accuracy']:.2%} ({evaluation['correct']}/{evaluation['total']})")
    
    # 详细结果
    print("\n详细分类结果:")
    for detail in evaluation['details']:
        status = "✓" if detail['correct'] else "✗"
        color_info = f"HSV{detail['dominant_color']}" if detail['dominant_color'] else "无颜色"
        print(f"{detail['filename']}: 预期={detail['expected']}, 预测={detail['predicted']}, "
              f"置信度={detail['confidence']:.3f}, 颜色={color_info} {status}")
    
    # 保存结果
    output_data = {
        'timestamp': datetime.now().isoformat(),
        'accuracy': evaluation['accuracy'],
        'results': results,
        'evaluation': evaluation
    }
    
    output_file = "precise_classification_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到: {output_file}")
    
    # 最终评估
    if evaluation['accuracy'] == 1.0:
        print("\n🎉 完美！达到100%准确率！")
    elif evaluation['accuracy'] >= 0.9:
        print(f"\n🌟 优秀！准确率达到{evaluation['accuracy']:.1%}")
    else:
        print(f"\n⚠️  准确率为{evaluation['accuracy']:.1%}，需要进一步优化")
        
        # 分析错误案例
        print("\n错误案例分析:")
        for detail in evaluation['details']:
            if not detail['correct']:
                print(f"  {detail['filename']}: {detail['expected']} -> {detail['predicted']}")
                print(f"    原因: {detail['reason']}, 颜色: HSV{detail['dominant_color']}")


if __name__ == "__main__":
    main()
