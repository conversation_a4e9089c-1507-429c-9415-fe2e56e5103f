# 股票交易信号颜色识别系统

基于颜色特征的股票交易信号自动识别系统，能够快速准确地识别截图中的交易信号类型。

## 功能特点

- **快速识别**: 基于颜色特征快速分类交易信号
- **高准确率**: 针对4种信号类型优化的颜色识别算法
- **批量处理**: 支持批量处理多张图像
- **可配置**: 支持自定义颜色范围和参数
- **调试模式**: 提供详细的调试信息和可视化
- **自适应**: 高级版本支持自适应颜色范围调整

## 支持的信号类型

| 信号类型 | 颜色特征 | 说明 |
|---------|---------|------|
| 持仓 | 蓝色背景 | 继续持有当前仓位 |
| 清仓 | 红色背景 | 卖出所有持仓 |
| 开仓 | 绿色背景 | 买入建立仓位 |
| 空仓 | 灰色背景 | 保持空仓状态 |

## 安装依赖

```bash
pip install opencv-python numpy matplotlib scikit-learn
```

## 快速开始

### 1. 基础使用

```python
from signal_classifier import SignalColorClassifier

# 创建分类器
classifier = SignalColorClassifier()

# 分类单张图像
result = classifier.classify_signal("signle/1.png")
print(f"信号类型: {result['signal_name']}")
print(f"置信度: {result['confidence']:.3f}")

# 批量处理
results = classifier.batch_classify("signle")
classifier.print_results(results)
```

### 2. 命令行使用

```bash
# 基础分类
python signal_classifier.py

# 高级分类（带自适应功能）
python advanced_signal_classifier.py --input-dir signle --adaptive --debug

# 运行测试
python test_classifier.py
```

## 技术实现方案

### 1. 图像预处理
- **颜色空间转换**: BGR → HSV，便于颜色特征提取
- **高斯模糊**: 去除图像噪声，提高识别稳定性
- **形态学操作**: 去除小的噪声点，连接断开的区域

### 2. 颜色特征提取
- **HSV颜色范围**: 为每种信号定义特定的HSV颜色范围
- **像素统计**: 计算每种颜色在图像中的占比
- **置信度计算**: 基于颜色占比确定分类置信度

### 3. 分类算法
- **最大占比法**: 选择占比最高的颜色作为分类结果
- **置信度阈值**: 设置最小置信度要求，过滤低质量结果
- **自适应调整**: 动态调整颜色范围以适应不同图像条件

### 4. 颜色范围定义 (HSV)

```python
color_ranges = {
    'hold': {   # 蓝色 - 持仓信号
        'lower': [100, 50, 50],
        'upper': [130, 255, 255]
    },
    'sell': {   # 红色 - 清仓信号
        'lower': [0, 50, 50],
        'upper': [10, 255, 255]
    },
    'buy': {    # 绿色 - 开仓信号
        'lower': [40, 50, 50],
        'upper': [80, 255, 255]
    },
    'empty': {  # 灰色 - 空仓信号
        'lower': [0, 0, 50],
        'upper': [180, 30, 200]
    }
}
```

## 使用示例

### 测试系统

运行测试脚本验证系统性能：

```bash
python test_classifier.py
```

预期输出：
```
股票交易信号分类器测试程序
当前工作目录: d:\Work\ZW\Stock\stock_screener

测试颜色范围设置
当前颜色范围设置:
持仓 (hold):
  HSV下限: [100  50  50]
  HSV上限: [130 255 255]

测试基础股票交易信号分类器
图像: signle/1.png
预期: 持仓
预测: 持仓
置信度: 0.850
状态: success
正确: ✓
```

### 实际应用

```python
# 处理新的交易信号截图
def process_trading_signal(image_path):
    classifier = SignalColorClassifier()
    result = classifier.classify_signal(image_path)
    
    if result['status'] == 'success':
        signal_type = result['signal_name']
        confidence = result['confidence']
        
        print(f"识别到交易信号: {signal_type}")
        print(f"置信度: {confidence:.2%}")
        
        # 根据信号类型执行相应操作
        if signal_type == '开仓':
            print("建议: 买入建立仓位")
        elif signal_type == '清仓':
            print("建议: 卖出所有持仓")
        elif signal_type == '持仓':
            print("建议: 继续持有")
        elif signal_type == '空仓':
            print("建议: 保持空仓")
            
        return signal_type, confidence
    else:
        print(f"识别失败: {result.get('error_message', '未知错误')}")
        return None, 0.0
```

## 性能优化建议

1. **预处理优化**:
   - 调整高斯模糊参数以适应图像质量
   - 使用形态学操作去除噪声

2. **颜色范围调整**:
   - 根据实际图像样本微调HSV范围
   - 考虑光照条件对颜色的影响

3. **置信度阈值**:
   - 根据准确率要求调整阈值
   - 平衡准确率和召回率

4. **批量处理**:
   - 使用多线程处理大量图像
   - 缓存预处理结果

## 扩展功能

### 1. 与OCR结合

```python
def enhanced_signal_recognition(image_path):
    # 先用颜色识别确定信号类型
    color_result = classifier.classify_signal(image_path)
    
    if color_result['confidence'] > 0.7:
        # 高置信度时直接返回颜色识别结果
        return color_result
    else:
        # 低置信度时结合OCR进行验证
        ocr_result = perform_ocr_recognition(image_path)
        return combine_color_and_ocr_results(color_result, ocr_result)
```

### 2. 实时监控

```python
def monitor_trading_signals(screenshot_dir):
    classifier = SignalColorClassifier()
    
    while True:
        # 监控新的截图文件
        new_images = get_new_screenshots(screenshot_dir)
        
        for image_path in new_images:
            result = classifier.classify_signal(image_path)
            
            if result['status'] == 'success':
                # 发送交易信号通知
                send_trading_alert(result)
        
        time.sleep(1)  # 每秒检查一次
```

## 故障排除

### 常见问题及解决方案

1. **识别准确率低**:
   - 检查图像质量和清晰度
   - 调整颜色范围参数
   - 增加训练样本

2. **颜色识别错误**:
   - 使用调试模式查看颜色分布
   - 调整HSV颜色范围
   - 考虑光照影响

3. **处理速度慢**:
   - 优化图像预处理步骤
   - 使用更小的图像尺寸
   - 并行处理多张图像

### 调试技巧

启用调试模式查看详细信息：

```python
classifier = AdvancedSignalColorClassifier()
classifier.debug_mode = True

# 会保存颜色掩码图像到debug_output目录
result = classifier.classify_signal("signle/1.png")
```

## 总结

这个股票交易信号颜色识别系统提供了：

1. **快速准确的信号识别**: 基于颜色特征的高效算法
2. **灵活的配置选项**: 可根据实际需求调整参数
3. **完整的处理流程**: 从图像预处理到结果输出
4. **扩展性强**: 易于集成到现有交易系统中

通过合理配置和优化，该系统能够显著提升交易信号识别的效率和准确性。
