#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能股票交易信号分类器
专门处理小面积信号颜色的情况
"""

import cv2
import numpy as np
from typing import Tuple, Dict, List
import os
import json
from datetime import datetime


class SmartSignalClassifier:
    """智能股票交易信号分类器"""
    
    def __init__(self, config_path: str = "realistic_color_config.json"):
        """初始化分类器"""
        self.load_config(config_path)
        
    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.signal_types = config['signal_types']
            self.color_ranges = {}
            
            for signal_type, range_data in config['color_ranges_hsv'].items():
                self.color_ranges[signal_type] = {
                    'lower': np.array(range_data['lower']),
                    'upper': np.array(range_data['upper']),
                    'description': range_data['description']
                }
            
            self.params = config['processing_parameters']
            
        except FileNotFoundError:
            print(f"配置文件 {config_path} 不存在，使用默认配置")
            self._use_default_config()
    
    def _use_default_config(self):
        """使用默认配置"""
        self.signal_types = {
            'hold': '持仓',
            'sell': '清仓', 
            'buy': '开仓',
            'empty': '空仓'
        }
        
        # 基于实际分析的颜色范围
        self.color_ranges = {
            'hold': {  # 持仓 - 红色系 HSV[0 255 192], HSV[10 191 255]
                'lower': np.array([0, 150, 150]),
                'upper': np.array([15, 255, 255]),
                'description': '持仓信号 - 红色系'
            },
            'sell': {  # 清仓 - 黄绿色系 HSV[60 255 166]
                'lower': np.array([50, 200, 100]),
                'upper': np.array([70, 255, 200]),
                'description': '清仓信号 - 黄绿色系'
            },
            'buy': {   # 开仓 - 红色系 HSV[0 255 255]
                'lower': np.array([0, 200, 200]),
                'upper': np.array([10, 255, 255]),
                'description': '开仓信号 - 红色系'
            },
            'empty': { # 空仓 - 灰色系 HSV[0 0 192]
                'lower': np.array([0, 0, 150]),
                'upper': np.array([180, 30, 220]),
                'description': '空仓信号 - 灰色系'
            }
        }
        
        self.params = {
            'confidence_threshold': 0.02,
            'gaussian_blur_kernel': [3, 3],
            'gaussian_blur_sigma': 0
        }
    
    def preprocess_image(self, image_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """图像预处理"""
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 转换为HSV色彩空间
        hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 轻微的高斯模糊
        kernel_size = tuple(self.params['gaussian_blur_kernel'])
        blurred = cv2.GaussianBlur(hsv_image, kernel_size, self.params['gaussian_blur_sigma'])
        
        return image, blurred
    
    def extract_non_black_features(self, hsv_image: np.ndarray) -> Dict[str, float]:
        """提取非黑色区域的颜色特征"""
        # 创建非黑色掩码 (排除HSV[0,0,0-50]的像素)
        black_mask = cv2.inRange(hsv_image, np.array([0, 0, 0]), np.array([180, 255, 50]))
        non_black_mask = cv2.bitwise_not(black_mask)
        
        # 计算非黑色像素总数
        non_black_pixels = cv2.countNonZero(non_black_mask)
        
        if non_black_pixels == 0:
            return {signal_type: 0.0 for signal_type in self.color_ranges.keys()}
        
        features = {}
        
        for signal_type, color_range in self.color_ranges.items():
            # 创建颜色掩码
            color_mask = cv2.inRange(hsv_image, color_range['lower'], color_range['upper'])
            
            # 只考虑非黑色区域的颜色匹配
            final_mask = cv2.bitwise_and(color_mask, non_black_mask)
            
            # 形态学操作去除噪声
            kernel = np.ones((2, 2), np.uint8)
            final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel)
            
            # 计算在非黑色区域中的占比
            color_pixels = cv2.countNonZero(final_mask)
            ratio = color_pixels / non_black_pixels if non_black_pixels > 0 else 0.0
            features[signal_type] = ratio
            
        return features
    
    def analyze_dominant_non_black_color(self, hsv_image: np.ndarray) -> Dict:
        """分析非黑色区域的主要颜色"""
        # 排除黑色像素
        mask = cv2.inRange(hsv_image, np.array([0, 0, 51]), np.array([180, 255, 255]))
        
        if cv2.countNonZero(mask) == 0:
            return {'dominant_color': None, 'pixel_count': 0}
        
        # 获取非黑色像素
        non_black_pixels = hsv_image[mask > 0]
        
        if len(non_black_pixels) == 0:
            return {'dominant_color': None, 'pixel_count': 0}
        
        # 计算主要颜色的统计信息
        h_values = non_black_pixels[:, 0]
        s_values = non_black_pixels[:, 1]
        v_values = non_black_pixels[:, 2]
        
        # 找到最常见的颜色
        unique_colors, counts = np.unique(non_black_pixels, axis=0, return_counts=True)
        dominant_idx = np.argmax(counts)
        dominant_color = unique_colors[dominant_idx]
        
        return {
            'dominant_color': dominant_color,
            'pixel_count': len(non_black_pixels),
            'h_range': (h_values.min(), h_values.max()),
            's_range': (s_values.min(), s_values.max()),
            'v_range': (v_values.min(), v_values.max())
        }
    
    def classify_signal(self, image_path: str) -> Dict:
        """分类交易信号"""
        try:
            # 预处理图像
            original_image, hsv_image = self.preprocess_image(image_path)
            
            # 分析非黑色区域的主要颜色
            color_analysis = self.analyze_dominant_non_black_color(hsv_image)
            
            # 提取颜色特征
            features = self.extract_non_black_features(hsv_image)
            
            # 找到最大占比的颜色
            max_signal = max(features.keys(), key=lambda k: features[k])
            max_confidence = features[max_signal]
            
            # 判断置信度
            if max_confidence < self.params['confidence_threshold']:
                # 如果所有颜色占比都很低，尝试基于主要颜色进行分类
                if color_analysis['dominant_color'] is not None:
                    dominant_color = color_analysis['dominant_color']
                    predicted_signal = self._classify_by_dominant_color(dominant_color)
                    
                    return {
                        'signal_type': predicted_signal,
                        'signal_name': self.signal_types.get(predicted_signal, '未知'),
                        'confidence': max_confidence,
                        'features': features,
                        'status': 'dominant_color_fallback',
                        'dominant_color_hsv': dominant_color.tolist(),
                        'non_black_pixels': color_analysis['pixel_count']
                    }
                else:
                    return {
                        'signal_type': 'unknown',
                        'signal_name': '未知',
                        'confidence': max_confidence,
                        'features': features,
                        'status': 'low_confidence'
                    }
            
            return {
                'signal_type': max_signal,
                'signal_name': self.signal_types[max_signal],
                'confidence': max_confidence,
                'features': features,
                'status': 'success',
                'dominant_color_hsv': color_analysis['dominant_color'].tolist() if color_analysis['dominant_color'] is not None else None,
                'non_black_pixels': color_analysis['pixel_count']
            }
            
        except Exception as e:
            return {
                'signal_type': 'error',
                'signal_name': '错误',
                'confidence': 0.0,
                'features': {},
                'status': 'error',
                'error_message': str(e)
            }
    
    def _classify_by_dominant_color(self, dominant_color: np.ndarray) -> str:
        """基于主要颜色进行分类"""
        h, s, v = dominant_color
        
        # 基于实际分析的颜色特征进行分类
        if s > 200 and v > 150:  # 高饱和度高亮度
            if h <= 15 or h >= 170:  # 红色系
                if v > 200:
                    return 'buy'  # 开仓 - 亮红色
                else:
                    return 'hold'  # 持仓 - 暗红色
            elif 50 <= h <= 70:  # 黄绿色系
                return 'sell'  # 清仓
        elif s < 50 and v > 150:  # 低饱和度高亮度 (灰色系)
            return 'empty'  # 空仓
        
        # 默认返回最可能的类型
        return 'hold'
    
    def batch_classify(self, image_dir: str) -> List[Dict]:
        """批量分类图像"""
        results = []
        image_dir_path = os.path.abspath(image_dir)
        
        # 支持的图像格式
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        
        for filename in sorted(os.listdir(image_dir_path)):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_path = os.path.join(image_dir_path, filename)
                print(f"处理图像: {filename}")
                
                result = self.classify_signal(image_path)
                result['filename'] = filename
                result['filepath'] = image_path
                results.append(result)
        
        return results
    
    def print_results(self, results: List[Dict]):
        """打印分类结果"""
        print("=" * 60)
        print("智能股票交易信号分类结果")
        print("=" * 60)
        
        for result in results:
            print(f"\n文件: {result['filename']}")
            print(f"信号类型: {result['signal_name']} ({result['signal_type']})")
            print(f"置信度: {result['confidence']:.4f}")
            print(f"状态: {result['status']}")
            
            if 'non_black_pixels' in result:
                print(f"非黑色像素数: {result['non_black_pixels']}")
            
            if 'dominant_color_hsv' in result and result['dominant_color_hsv']:
                print(f"主要颜色 HSV: {result['dominant_color_hsv']}")
            
            if result['status'] == 'error':
                print(f"错误信息: {result.get('error_message', '未知错误')}")
            
            # 显示颜色特征
            if result['features']:
                print("颜色特征:")
                for signal_type, ratio in result['features'].items():
                    signal_name = self.signal_types.get(signal_type, signal_type)
                    print(f"  {signal_name}: {ratio:.4f}")


def main():
    """主函数"""
    print("智能股票交易信号分类器")
    
    # 创建分类器
    classifier = SmartSignalClassifier()
    
    # 设置图像目录
    image_dir = "signle"
    
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录 '{image_dir}' 不存在")
        return
    
    # 批量分类
    print("开始分析股票交易信号图像...")
    results = classifier.batch_classify(image_dir)
    
    # 打印结果
    classifier.print_results(results)
    
    # 保存结果
    output_file = "smart_classification_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        output_data = {
            'timestamp': datetime.now().isoformat(),
            'total_images': len(results),
            'results': results
        }
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到: {output_file}")
    
    # 统计准确率
    expected_signals = {
        '1.png': '持仓',
        '2.png': '清仓',
        '3.png': '开仓', 
        '4.png': '空仓',
        '5.png': '持仓',
        '6.png': '清仓',
        '7.png': '开仓',
        '8.png': '空仓'
    }
    
    correct = 0
    total = 0
    
    for result in results:
        filename = result['filename']
        if filename in expected_signals:
            expected = expected_signals[filename]
            predicted = result['signal_name']
            if expected == predicted:
                correct += 1
            total += 1
    
    if total > 0:
        accuracy = correct / total
        print(f"\n准确率: {accuracy:.2%} ({correct}/{total})")


if __name__ == "__main__":
    main()
