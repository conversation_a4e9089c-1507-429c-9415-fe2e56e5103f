#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级股票交易信号颜色识别分类器
包含自适应颜色范围调整、多种特征提取方法和可视化功能
"""

import cv2
import numpy as np
from typing import Tuple, Dict, List, Optional
import os
import json
from pathlib import Path
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
import argparse


class AdvancedSignalColorClassifier:
    """高级股票交易信号颜色分类器"""
    
    def __init__(self, config_path: str = "color_config.json"):
        """
        初始化分类器
        
        Args:
            config_path: 配置文件路径
        """
        self.load_config(config_path)
        self.debug_mode = False
        
    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.signal_types = config['signal_types']
            self.color_ranges = {}
            
            # 转换颜色范围为numpy数组
            for signal_type, range_data in config['color_ranges_hsv'].items():
                self.color_ranges[signal_type] = {
                    'lower': np.array(range_data['lower']),
                    'upper': np.array(range_data['upper']),
                    'description': range_data['description']
                }
            
            self.params = config['processing_parameters']
            self.output_settings = config['output_settings']
            
        except FileNotFoundError:
            print(f"配置文件 {config_path} 不存在，使用默认配置")
            self._use_default_config()
    
    def _use_default_config(self):
        """使用默认配置"""
        self.signal_types = {
            'hold': '持仓',
            'sell': '清仓', 
            'buy': '开仓',
            'empty': '空仓'
        }
        
        self.color_ranges = {
            'hold': {
                'lower': np.array([100, 50, 50]),
                'upper': np.array([130, 255, 255]),
                'description': '蓝色 - 持仓信号'
            },
            'sell': {
                'lower': np.array([0, 50, 50]),
                'upper': np.array([10, 255, 255]),
                'description': '红色 - 清仓信号'
            },
            'buy': {
                'lower': np.array([40, 50, 50]),
                'upper': np.array([80, 255, 255]),
                'description': '绿色 - 开仓信号'
            },
            'empty': {
                'lower': np.array([0, 0, 50]),
                'upper': np.array([180, 30, 200]),
                'description': '灰色 - 空仓信号'
            }
        }
        
        self.params = {
            'confidence_threshold': 0.3,
            'gaussian_blur_kernel': [5, 5],
            'gaussian_blur_sigma': 0
        }
        
        self.output_settings = {
            'save_debug_images': False,
            'debug_output_dir': 'debug_output'
        }
    
    def preprocess_image(self, image_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        增强的图像预处理
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            (原始图像, HSV图像)
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 转换为HSV色彩空间
        hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 高斯模糊去噪
        kernel_size = tuple(self.params['gaussian_blur_kernel'])
        sigma = self.params['gaussian_blur_sigma']
        blurred = cv2.GaussianBlur(hsv_image, kernel_size, sigma)
        
        return image, blurred
    
    def extract_dominant_colors(self, image: np.ndarray, k: int = 3) -> List[Tuple]:
        """
        使用K-means提取主要颜色
        
        Args:
            image: 输入图像
            k: 聚类数量
            
        Returns:
            主要颜色列表
        """
        # 重塑图像为像素列表
        pixels = image.reshape(-1, 3)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(pixels)
        
        # 获取聚类中心（主要颜色）
        colors = kmeans.cluster_centers_.astype(int)
        
        # 计算每个颜色的权重
        labels = kmeans.labels_
        weights = []
        for i in range(k):
            weight = np.sum(labels == i) / len(labels)
            weights.append(weight)
        
        return list(zip(colors, weights))
    
    def extract_color_features(self, hsv_image: np.ndarray) -> Dict[str, float]:
        """
        提取颜色特征
        
        Args:
            hsv_image: HSV格式的图像
            
        Returns:
            各颜色的占比字典
        """
        features = {}
        total_pixels = hsv_image.shape[0] * hsv_image.shape[1]
        
        for signal_type, color_range in self.color_ranges.items():
            # 创建颜色掩码
            mask = cv2.inRange(hsv_image, color_range['lower'], color_range['upper'])
            
            # 形态学操作去除噪声
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            
            # 计算颜色占比
            color_pixels = cv2.countNonZero(mask)
            ratio = color_pixels / total_pixels
            features[signal_type] = ratio
            
        return features
    
    def adaptive_color_range(self, hsv_image: np.ndarray, base_signal: str) -> Dict:
        """
        自适应调整颜色范围
        
        Args:
            hsv_image: HSV图像
            base_signal: 基础信号类型
            
        Returns:
            调整后的颜色范围
        """
        # 提取主要颜色
        dominant_colors = self.extract_dominant_colors(hsv_image)
        
        if not dominant_colors:
            return self.color_ranges[base_signal]
        
        # 找到最主要的颜色
        main_color, weight = max(dominant_colors, key=lambda x: x[1])
        
        # 转换为HSV
        main_color_bgr = np.uint8([[main_color]])
        main_color_hsv = cv2.cvtColor(main_color_bgr, cv2.COLOR_BGR2HSV)[0][0]
        
        # 基于主要颜色调整范围
        h, s, v = main_color_hsv
        
        # 动态调整范围
        h_range = 15
        s_range = 50
        v_range = 50
        
        lower = np.array([max(0, h - h_range), max(0, s - s_range), max(0, v - v_range)])
        upper = np.array([min(179, h + h_range), min(255, s + s_range), min(255, v + v_range)])
        
        return {
            'lower': lower,
            'upper': upper,
            'description': f'自适应调整 - 基于主色调 HSV({h}, {s}, {v})'
        }
    
    def classify_signal(self, image_path: str, use_adaptive: bool = False) -> Dict:
        """
        分类交易信号
        
        Args:
            image_path: 图像文件路径
            use_adaptive: 是否使用自适应颜色范围
            
        Returns:
            分类结果字典
        """
        try:
            # 预处理图像
            original_image, hsv_image = self.preprocess_image(image_path)
            
            # 提取颜色特征
            features = self.extract_color_features(hsv_image)
            
            # 找到最大占比的颜色
            max_signal = max(features.keys(), key=lambda k: features[k])
            max_confidence = features[max_signal]
            
            # 如果使用自适应范围且置信度较低，尝试自适应调整
            if use_adaptive and max_confidence < self.params['confidence_threshold']:
                adaptive_range = self.adaptive_color_range(hsv_image, max_signal)
                # 重新计算特征（这里简化处理）
                pass
            
            # 判断置信度
            status = 'success' if max_confidence >= self.params['confidence_threshold'] else 'low_confidence'
            
            result = {
                'signal_type': max_signal,
                'signal_name': self.signal_types[max_signal],
                'confidence': max_confidence,
                'features': features,
                'status': status,
                'image_size': hsv_image.shape[:2],
                'processing_method': 'adaptive' if use_adaptive else 'standard'
            }
            
            # 保存调试图像
            if self.debug_mode and self.output_settings.get('save_debug_images', False):
                self._save_debug_images(original_image, hsv_image, features, image_path)
            
            return result
            
        except Exception as e:
            return {
                'signal_type': 'error',
                'signal_name': '错误',
                'confidence': 0.0,
                'features': {},
                'status': 'error',
                'error_message': str(e)
            }
    
    def _save_debug_images(self, original: np.ndarray, hsv: np.ndarray, 
                          features: Dict, image_path: str):
        """保存调试图像"""
        debug_dir = Path(self.output_settings.get('debug_output_dir', 'debug_output'))
        debug_dir.mkdir(exist_ok=True)
        
        filename = Path(image_path).stem
        
        # 保存颜色掩码
        for signal_type, color_range in self.color_ranges.items():
            mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])
            mask_path = debug_dir / f"{filename}_{signal_type}_mask.png"
            cv2.imwrite(str(mask_path), mask)
    
    def batch_classify(self, image_dir: str, use_adaptive: bool = False) -> List[Dict]:
        """
        批量分类图像
        
        Args:
            image_dir: 图像目录路径
            use_adaptive: 是否使用自适应颜色范围
            
        Returns:
            分类结果列表
        """
        results = []
        image_dir = Path(image_dir)
        
        # 支持的图像格式
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        
        for image_file in sorted(image_dir.iterdir()):
            if image_file.suffix.lower() in image_extensions:
                print(f"处理图像: {image_file.name}")
                result = self.classify_signal(str(image_file), use_adaptive)
                result['filename'] = image_file.name
                result['filepath'] = str(image_file)
                results.append(result)
        
        return results
    
    def generate_report(self, results: List[Dict]) -> Dict:
        """
        生成分析报告
        
        Args:
            results: 分类结果列表
            
        Returns:
            分析报告
        """
        total_images = len(results)
        successful = sum(1 for r in results if r['status'] == 'success')
        low_confidence = sum(1 for r in results if r['status'] == 'low_confidence')
        errors = sum(1 for r in results if r['status'] == 'error')
        
        # 统计各信号类型
        signal_counts = {}
        for result in results:
            if result['status'] in ['success', 'low_confidence']:
                signal_type = result['signal_type']
                signal_counts[signal_type] = signal_counts.get(signal_type, 0) + 1
        
        # 计算平均置信度
        confidences = [r['confidence'] for r in results if r['status'] != 'error']
        avg_confidence = np.mean(confidences) if confidences else 0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_images': total_images,
                'successful_classifications': successful,
                'low_confidence_classifications': low_confidence,
                'errors': errors,
                'success_rate': successful / total_images if total_images > 0 else 0,
                'average_confidence': avg_confidence
            },
            'signal_distribution': signal_counts,
            'detailed_results': results
        }
        
        return report


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票交易信号颜色识别分类器')
    parser.add_argument('--input-dir', default='signle', help='输入图像目录')
    parser.add_argument('--config', default='color_config.json', help='配置文件路径')
    parser.add_argument('--output', default='signal_analysis_report.json', help='输出报告文件')
    parser.add_argument('--adaptive', action='store_true', help='使用自适应颜色范围')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    # 创建分类器
    classifier = AdvancedSignalColorClassifier(args.config)
    classifier.debug_mode = args.debug
    
    if not os.path.exists(args.input_dir):
        print(f"错误: 输入目录 '{args.input_dir}' 不存在")
        return
    
    # 批量分类
    print(f"开始分析目录 '{args.input_dir}' 中的股票交易信号图像...")
    results = classifier.batch_classify(args.input_dir, args.adaptive)
    
    # 生成报告
    report = classifier.generate_report(results)
    
    # 保存报告
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 打印摘要
    summary = report['summary']
    print("\n" + "=" * 60)
    print("分析报告摘要")
    print("=" * 60)
    print(f"总图像数: {summary['total_images']}")
    print(f"成功分类: {summary['successful_classifications']}")
    print(f"低置信度: {summary['low_confidence_classifications']}")
    print(f"错误数: {summary['errors']}")
    print(f"成功率: {summary['success_rate']:.2%}")
    print(f"平均置信度: {summary['average_confidence']:.3f}")
    
    print(f"\n详细报告已保存到: {args.output}")


if __name__ == "__main__":
    main()
