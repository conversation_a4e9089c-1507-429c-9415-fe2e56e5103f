#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票交易信号颜色识别分类器
基于颜色特征快速识别交易信号类型
"""

import cv2
import numpy as np
from typing import Tuple, Dict, List
import os
from pathlib import Path
import json
from datetime import datetime


class SignalColorClassifier:
    """股票交易信号颜色分类器"""
    
    def __init__(self):
        """初始化分类器"""
        # 定义信号类型
        self.signal_types = {
            'hold': '持仓',
            'sell': '清仓', 
            'buy': '开仓',
            'empty': '空仓'
        }
        
        # 定义颜色范围 (HSV色彩空间)
        self.color_ranges = {
            'hold': {  # 蓝色 - 持仓信号
                'lower': np.array([100, 50, 50]),
                'upper': np.array([130, 255, 255])
            },
            'sell': {  # 红色 - 清仓信号
                'lower': np.array([0, 50, 50]),
                'upper': np.array([10, 255, 255])
            },
            'buy': {   # 绿色 - 开仓信号
                'lower': np.array([40, 50, 50]),
                'upper': np.array([80, 255, 255])
            },
            'empty': { # 灰色 - 空仓信号
                'lower': np.array([0, 0, 50]),
                'upper': np.array([180, 30, 200])
            }
        }
        
        # 置信度阈值
        self.confidence_threshold = 0.3
        
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """
        图像预处理
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            预处理后的图像数组
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 转换为HSV色彩空间
        hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(hsv_image, (5, 5), 0)
        
        return blurred
    
    def extract_color_features(self, hsv_image: np.ndarray) -> Dict[str, float]:
        """
        提取颜色特征
        
        Args:
            hsv_image: HSV格式的图像
            
        Returns:
            各颜色的占比字典
        """
        features = {}
        total_pixels = hsv_image.shape[0] * hsv_image.shape[1]
        
        for signal_type, color_range in self.color_ranges.items():
            # 创建颜色掩码
            mask = cv2.inRange(hsv_image, color_range['lower'], color_range['upper'])
            
            # 计算颜色占比
            color_pixels = cv2.countNonZero(mask)
            ratio = color_pixels / total_pixels
            features[signal_type] = ratio
            
        return features
    
    def classify_signal(self, image_path: str) -> Dict:
        """
        分类交易信号
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            分类结果字典
        """
        try:
            # 预处理图像
            hsv_image = self.preprocess_image(image_path)
            
            # 提取颜色特征
            features = self.extract_color_features(hsv_image)
            
            # 找到最大占比的颜色
            max_signal = max(features.keys(), key=lambda k: features[k])
            max_confidence = features[max_signal]
            
            # 判断置信度
            if max_confidence < self.confidence_threshold:
                return {
                    'signal_type': 'unknown',
                    'signal_name': '未知',
                    'confidence': max_confidence,
                    'features': features,
                    'status': 'low_confidence'
                }
            
            return {
                'signal_type': max_signal,
                'signal_name': self.signal_types[max_signal],
                'confidence': max_confidence,
                'features': features,
                'status': 'success'
            }
            
        except Exception as e:
            return {
                'signal_type': 'error',
                'signal_name': '错误',
                'confidence': 0.0,
                'features': {},
                'status': 'error',
                'error_message': str(e)
            }
    
    def batch_classify(self, image_dir: str) -> List[Dict]:
        """
        批量分类图像
        
        Args:
            image_dir: 图像目录路径
            
        Returns:
            分类结果列表
        """
        results = []
        image_dir = Path(image_dir)
        
        # 支持的图像格式
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        
        for image_file in image_dir.iterdir():
            if image_file.suffix.lower() in image_extensions:
                result = self.classify_signal(str(image_file))
                result['filename'] = image_file.name
                result['filepath'] = str(image_file)
                results.append(result)
        
        return results
    
    def save_results(self, results: List[Dict], output_path: str):
        """
        保存分类结果
        
        Args:
            results: 分类结果列表
            output_path: 输出文件路径
        """
        output_data = {
            'timestamp': datetime.now().isoformat(),
            'total_images': len(results),
            'results': results
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    def print_results(self, results: List[Dict]):
        """
        打印分类结果
        
        Args:
            results: 分类结果列表
        """
        print("=" * 60)
        print("股票交易信号分类结果")
        print("=" * 60)
        
        for result in results:
            print(f"\n文件: {result['filename']}")
            print(f"信号类型: {result['signal_name']} ({result['signal_type']})")
            print(f"置信度: {result['confidence']:.3f}")
            print(f"状态: {result['status']}")
            
            if result['status'] == 'error':
                print(f"错误信息: {result.get('error_message', '未知错误')}")
            
            # 显示颜色特征
            if result['features']:
                print("颜色特征:")
                for signal_type, ratio in result['features'].items():
                    signal_name = self.signal_types.get(signal_type, signal_type)
                    print(f"  {signal_name}: {ratio:.3f}")


def main():
    """主函数 - 演示用法"""
    # 创建分类器
    classifier = SignalColorClassifier()
    
    # 设置图像目录
    image_dir = "signle"
    
    if not os.path.exists(image_dir):
        print(f"错误: 图像目录 '{image_dir}' 不存在")
        return
    
    # 批量分类
    print("开始分析股票交易信号图像...")
    results = classifier.batch_classify(image_dir)
    
    # 打印结果
    classifier.print_results(results)
    
    # 保存结果
    output_file = "signal_classification_results.json"
    classifier.save_results(results, output_file)
    print(f"\n结果已保存到: {output_file}")


if __name__ == "__main__":
    main()
