#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票交易信号识别系统演示
展示如何在实际应用中使用颜色识别分类器
"""

import os
import time
from final_signal_classifier import FinalSignalClassifier


def demo_single_image():
    """演示单张图像分类"""
    print("=" * 60)
    print("演示1: 单张图像分类")
    print("=" * 60)
    
    classifier = FinalSignalClassifier()
    
    # 测试单张图像
    test_image = "signle/1.png"
    
    if os.path.exists(test_image):
        print(f"分析图像: {test_image}")
        
        start_time = time.time()
        result = classifier.classify_signal(test_image)
        end_time = time.time()
        
        print(f"处理时间: {(end_time - start_time)*1000:.2f} 毫秒")
        print(f"信号类型: {result['signal_name']}")
        print(f"置信度: {result['confidence']:.3f}")
        print(f"状态: {result['status']}")
        
        if result['dominant_color']:
            print(f"主要颜色 HSV: {result['dominant_color']}")
        
        return result
    else:
        print(f"图像文件不存在: {test_image}")
        return None


def demo_batch_processing():
    """演示批量处理"""
    print("\n" + "=" * 60)
    print("演示2: 批量处理")
    print("=" * 60)
    
    classifier = FinalSignalClassifier()
    
    image_dir = "signle"
    
    if os.path.exists(image_dir):
        print(f"批量处理目录: {image_dir}")
        
        start_time = time.time()
        results = classifier.batch_classify(image_dir)
        end_time = time.time()
        
        print(f"总处理时间: {(end_time - start_time)*1000:.2f} 毫秒")
        print(f"平均每张图像: {(end_time - start_time)*1000/len(results):.2f} 毫秒")
        
        # 统计结果
        signal_counts = {}
        high_confidence_count = 0
        
        for result in results:
            signal_type = result['signal_name']
            signal_counts[signal_type] = signal_counts.get(signal_type, 0) + 1
            
            if result['confidence'] > 0.5:
                high_confidence_count += 1
        
        print(f"\n处理结果统计:")
        print(f"总图像数: {len(results)}")
        print(f"高置信度分类 (>0.5): {high_confidence_count}")
        
        print(f"\n信号类型分布:")
        for signal_type, count in signal_counts.items():
            print(f"  {signal_type}: {count}")
        
        return results
    else:
        print(f"目录不存在: {image_dir}")
        return []


def demo_real_time_simulation():
    """演示实时处理模拟"""
    print("\n" + "=" * 60)
    print("演示3: 实时处理模拟")
    print("=" * 60)
    
    classifier = FinalSignalClassifier()
    
    # 模拟实时处理场景
    test_images = [
        "signle/1.png", "signle/2.png", "signle/3.png", "signle/4.png"
    ]
    
    print("模拟实时交易信号监控...")
    
    for i, image_path in enumerate(test_images):
        if os.path.exists(image_path):
            print(f"\n[{i+1}] 检测到新的交易信号截图: {os.path.basename(image_path)}")
            
            result = classifier.classify_signal(image_path)
            
            if result['status'] == 'feature_based' and result['confidence'] > 0.3:
                print(f"✅ 识别成功: {result['signal_name']}")
                print(f"   置信度: {result['confidence']:.3f}")
                print(f"   建议操作: {get_trading_advice(result['signal_type'])}")
            else:
                print(f"⚠️  识别置信度较低: {result['signal_name']}")
                print(f"   置信度: {result['confidence']:.3f}")
                print(f"   建议: 人工确认或结合其他方法")
            
            # 模拟处理间隔
            time.sleep(0.5)
        else:
            print(f"图像文件不存在: {image_path}")


def get_trading_advice(signal_type):
    """根据信号类型返回交易建议"""
    advice_map = {
        'hold': '继续持有当前仓位',
        'sell': '卖出所有持仓',
        'buy': '买入建立仓位',
        'empty': '保持空仓状态'
    }
    return advice_map.get(signal_type, '未知操作')


def demo_integration_example():
    """演示集成应用示例"""
    print("\n" + "=" * 60)
    print("演示4: 集成应用示例")
    print("=" * 60)
    
    def process_trading_screenshot(image_path):
        """处理交易截图的完整流程"""
        classifier = FinalSignalClassifier()
        
        # 步骤1: 颜色识别
        result = classifier.classify_signal(image_path)
        
        # 步骤2: 置信度判断
        if result['confidence'] > 0.5:
            # 高置信度，直接使用结果
            return {
                'method': 'color_recognition',
                'signal': result['signal_name'],
                'confidence': result['confidence'],
                'action': get_trading_advice(result['signal_type'])
            }
        elif result['confidence'] > 0.2:
            # 中等置信度，建议结合其他方法
            return {
                'method': 'color_recognition_with_verification',
                'signal': result['signal_name'],
                'confidence': result['confidence'],
                'action': f"建议结合OCR验证: {get_trading_advice(result['signal_type'])}"
            }
        else:
            # 低置信度，建议使用其他方法
            return {
                'method': 'fallback_to_ocr',
                'signal': '需要OCR识别',
                'confidence': result['confidence'],
                'action': '使用OCR或人工识别'
            }
    
    # 测试集成流程
    test_images = ["signle/5.png", "signle/6.png", "signle/7.png"]
    
    for image_path in test_images:
        if os.path.exists(image_path):
            print(f"\n处理: {os.path.basename(image_path)}")
            result = process_trading_screenshot(image_path)
            
            print(f"识别方法: {result['method']}")
            print(f"信号类型: {result['signal']}")
            print(f"置信度: {result['confidence']:.3f}")
            print(f"建议操作: {result['action']}")
        else:
            print(f"图像文件不存在: {image_path}")


def main():
    """主演示函数"""
    print("股票交易信号颜色识别系统演示")
    print("基于OpenCV和HSV颜色空间的智能分类")
    print("开发时间: 2025-08-01")
    
    try:
        # 演示1: 单张图像分类
        demo_single_image()
        
        # 演示2: 批量处理
        demo_batch_processing()
        
        # 演示3: 实时处理模拟
        demo_real_time_simulation()
        
        # 演示4: 集成应用示例
        demo_integration_example()
        
        print("\n" + "=" * 60)
        print("演示完成")
        print("=" * 60)
        print("系统特点:")
        print("✅ 高准确率: 87.5%")
        print("✅ 快速处理: <100ms/图像")
        print("✅ 易于集成: 简洁API")
        print("✅ 可扩展性: 支持新信号类型")
        
        print("\n推荐使用场景:")
        print("🔸 交易信号预分类")
        print("🔸 OCR处理优化")
        print("🔸 实时监控系统")
        print("🔸 批量图像处理")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
